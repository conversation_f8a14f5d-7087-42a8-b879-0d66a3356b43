package com.bikeana.listing.domain;

/**
 * Enumeration for photo types to categorize bike listing photos.
 */
public enum PhotoType {
    DRIVE_SIDE_FULL("Drive-side full-bike view"),
    NON_DRIVE_SIDE_FULL("Non-drive-side full-bike view"),
    DRIVETRAIN_CLOSEUP("Drivetrain close-up"),
    BRAKE_CLOSEUP("Brake close-up"),
    FRAME_DETAIL("Frame detail close-up"),
    GENERAL("General photo");

    private final String displayName;

    PhotoType(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }
}
