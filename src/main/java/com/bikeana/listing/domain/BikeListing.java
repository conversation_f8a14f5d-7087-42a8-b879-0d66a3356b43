package com.bikeana.listing.domain;

import com.bikeana.user.domain.User;
import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Entity representing a bike listing in the marketplace.
 * Contains all bike specifications, pricing, and metadata.
 */
@Entity
@Table(name = "bike_listings")
public class BikeListing {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    // General Information
    @Column(nullable = false, length = 200)
    private String title;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ListingStatus status = ListingStatus.ACTIVE;

    // Bike Specifications
    @Column(nullable = false, length = 100)
    private String brand;

    @Column(nullable = false, length = 100)
    private String model;

    @Column(nullable = false)
    private Integer year;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private FrameMaterial frameMaterial;

    @Column(nullable = false, length = 50)
    private String frameSize;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private BikeType bikeType;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private WheelSize wheelSize;

    @Column(nullable = false)
    private Integer drivetrainSpeeds;

    @Column(nullable = false, length = 100)
    private String groupsetBrand;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private BrakeType brakeType;

    @Column(nullable = false, length = 50)
    private String color;

    // Condition & History
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ConditionTier conditionTier;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String conditionNotes;

    @Column(columnDefinition = "TEXT")
    private String serviceHistory;

    @Column(nullable = false, unique = true, length = 100)
    private String serialNumber;

    @Column(columnDefinition = "TEXT")
    private String warrantyInformation;

    // Pricing & Sales Terms
    @Column(nullable = false, precision = 10, scale = 2)
    private BigDecimal askingPrice;

    @Column(nullable = false)
    private Boolean negotiable;

    @Column(precision = 10, scale = 2)
    private BigDecimal depositRequired;

    // Categorization & Search
    @Column(length = 500)
    private String tags;

    @Column(nullable = false, unique = true, length = 200)
    private String seoSlug;

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "seller_id", nullable = false)
    private User seller;

    @OneToMany(mappedBy = "listing", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<BikeListingPhoto> photos = new ArrayList<>();

    @OneToOne(mappedBy = "listing", cascade = CascadeType.ALL, orphanRemoval = true)
    private BikeListingLocation location;

    // Availability & Reservation
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private AvailabilityStatus availabilityStatus = AvailabilityStatus.AVAILABLE;

    @Column
    private UUID reservedByUserId;

    @Column
    private LocalDateTime reservationExpiresAt;

    // Audit fields
    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(nullable = false)
    private LocalDateTime updatedAt;

    // Constructors
    public BikeListing() {}

    // Getters and Setters
    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public ListingStatus getStatus() {
        return status;
    }

    public void setStatus(ListingStatus status) {
        this.status = status;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public FrameMaterial getFrameMaterial() {
        return frameMaterial;
    }

    public void setFrameMaterial(FrameMaterial frameMaterial) {
        this.frameMaterial = frameMaterial;
    }

    public String getFrameSize() {
        return frameSize;
    }

    public void setFrameSize(String frameSize) {
        this.frameSize = frameSize;
    }

    public BikeType getBikeType() {
        return bikeType;
    }

    public void setBikeType(BikeType bikeType) {
        this.bikeType = bikeType;
    }

    public WheelSize getWheelSize() {
        return wheelSize;
    }

    public void setWheelSize(WheelSize wheelSize) {
        this.wheelSize = wheelSize;
    }

    public Integer getDrivetrainSpeeds() {
        return drivetrainSpeeds;
    }

    public void setDrivetrainSpeeds(Integer drivetrainSpeeds) {
        this.drivetrainSpeeds = drivetrainSpeeds;
    }

    public String getGroupsetBrand() {
        return groupsetBrand;
    }

    public void setGroupsetBrand(String groupsetBrand) {
        this.groupsetBrand = groupsetBrand;
    }

    public BrakeType getBrakeType() {
        return brakeType;
    }

    public void setBrakeType(BrakeType brakeType) {
        this.brakeType = brakeType;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public ConditionTier getConditionTier() {
        return conditionTier;
    }

    public void setConditionTier(ConditionTier conditionTier) {
        this.conditionTier = conditionTier;
    }

    public String getConditionNotes() {
        return conditionNotes;
    }

    public void setConditionNotes(String conditionNotes) {
        this.conditionNotes = conditionNotes;
    }

    public String getServiceHistory() {
        return serviceHistory;
    }

    public void setServiceHistory(String serviceHistory) {
        this.serviceHistory = serviceHistory;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getWarrantyInformation() {
        return warrantyInformation;
    }

    public void setWarrantyInformation(String warrantyInformation) {
        this.warrantyInformation = warrantyInformation;
    }

    public BigDecimal getAskingPrice() {
        return askingPrice;
    }

    public void setAskingPrice(BigDecimal askingPrice) {
        this.askingPrice = askingPrice;
    }

    public Boolean getNegotiable() {
        return negotiable;
    }

    public void setNegotiable(Boolean negotiable) {
        this.negotiable = negotiable;
    }

    public BigDecimal getDepositRequired() {
        return depositRequired;
    }

    public void setDepositRequired(BigDecimal depositRequired) {
        this.depositRequired = depositRequired;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getSeoSlug() {
        return seoSlug;
    }

    public void setSeoSlug(String seoSlug) {
        this.seoSlug = seoSlug;
    }

    public User getSeller() {
        return seller;
    }

    public void setSeller(User seller) {
        this.seller = seller;
    }

    public List<BikeListingPhoto> getPhotos() {
        return photos;
    }

    public void setPhotos(List<BikeListingPhoto> photos) {
        this.photos = photos;
    }

    public BikeListingLocation getLocation() {
        return location;
    }

    public void setLocation(BikeListingLocation location) {
        this.location = location;
    }

    public AvailabilityStatus getAvailabilityStatus() {
        return availabilityStatus;
    }

    public void setAvailabilityStatus(AvailabilityStatus availabilityStatus) {
        this.availabilityStatus = availabilityStatus;
    }

    public UUID getReservedByUserId() {
        return reservedByUserId;
    }

    public void setReservedByUserId(UUID reservedByUserId) {
        this.reservedByUserId = reservedByUserId;
    }

    public LocalDateTime getReservationExpiresAt() {
        return reservationExpiresAt;
    }

    public void setReservationExpiresAt(LocalDateTime reservationExpiresAt) {
        this.reservationExpiresAt = reservationExpiresAt;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}
