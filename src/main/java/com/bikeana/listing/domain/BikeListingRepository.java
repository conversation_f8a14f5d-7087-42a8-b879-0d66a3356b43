package com.bikeana.listing.domain;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for bike listing data access operations.
 */
public interface BikeListingRepository extends JpaRepository<BikeListing, UUID> {

    /**
     * Finds all active listings with pagination.
     */
    @Query("SELECT bl FROM BikeListing bl WHERE bl.status = 'ACTIVE' AND bl.availabilityStatus = 'AVAILABLE' ORDER BY bl.createdAt DESC")
    Page<BikeListing> findActiveListings(Pageable pageable);

    /**
     * Finds listings by seller ID.
     */
    @Query("SELECT bl FROM BikeListing bl WHERE bl.seller.id = :sellerId ORDER BY bl.createdAt DESC")
    List<BikeListing> findBySellerIdOrderByCreatedAtDesc(@Param("sellerId") UUID sellerId);

    /**
     * Finds active listings by seller ID.
     */
    @Query("SELECT bl FROM BikeListing bl WHERE bl.seller.id = :sellerId AND bl.status = 'ACTIVE' ORDER BY bl.createdAt DESC")
    List<BikeListing> findActiveListingsBySellerId(@Param("sellerId") UUID sellerId);

    /**
     * Finds listing by SEO slug.
     */
    Optional<BikeListing> findBySeoSlug(String seoSlug);

    /**
     * Checks if a serial number already exists.
     */
    boolean existsBySerialNumber(String serialNumber);

    /**
     * Checks if a SEO slug already exists.
     */
    boolean existsBySeoSlug(String seoSlug);

    /**
     * Finds listings by bike type.
     */
    @Query("SELECT bl FROM BikeListing bl WHERE bl.bikeType = :bikeType AND bl.status = 'ACTIVE' AND bl.availabilityStatus = 'AVAILABLE' ORDER BY bl.createdAt DESC")
    Page<BikeListing> findByBikeType(@Param("bikeType") BikeType bikeType, Pageable pageable);

    /**
     * Finds listings by price range.
     */
    @Query("SELECT bl FROM BikeListing bl WHERE bl.askingPrice BETWEEN :minPrice AND :maxPrice AND bl.status = 'ACTIVE' AND bl.availabilityStatus = 'AVAILABLE' ORDER BY bl.createdAt DESC")
    Page<BikeListing> findByPriceRange(@Param("minPrice") BigDecimal minPrice, @Param("maxPrice") BigDecimal maxPrice, Pageable pageable);

    /**
     * Finds listings by brand (case-insensitive).
     */
    @Query("SELECT bl FROM BikeListing bl WHERE LOWER(bl.brand) LIKE LOWER(CONCAT('%', :brand, '%')) AND bl.status = 'ACTIVE' AND bl.availabilityStatus = 'AVAILABLE' ORDER BY bl.createdAt DESC")
    Page<BikeListing> findByBrandContainingIgnoreCase(@Param("brand") String brand, Pageable pageable);

    /**
     * Finds listings by condition tier.
     */
    @Query("SELECT bl FROM BikeListing bl WHERE bl.conditionTier = :conditionTier AND bl.status = 'ACTIVE' AND bl.availabilityStatus = 'AVAILABLE' ORDER BY bl.createdAt DESC")
    Page<BikeListing> findByConditionTier(@Param("conditionTier") ConditionTier conditionTier, Pageable pageable);

    /**
     * Search listings by title or description (full-text search).
     */
    @Query("SELECT bl FROM BikeListing bl WHERE (LOWER(bl.title) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR LOWER(bl.description) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR LOWER(bl.brand) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR LOWER(bl.model) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) AND bl.status = 'ACTIVE' AND bl.availabilityStatus = 'AVAILABLE' ORDER BY bl.createdAt DESC")
    Page<BikeListing> searchListings(@Param("searchTerm") String searchTerm, Pageable pageable);

    /**
     * Finds listings by multiple criteria.
     */
    @Query("SELECT bl FROM BikeListing bl WHERE " +
           "(:bikeType IS NULL OR bl.bikeType = :bikeType) AND " +
           "(:minPrice IS NULL OR bl.askingPrice >= :minPrice) AND " +
           "(:maxPrice IS NULL OR bl.askingPrice <= :maxPrice) AND " +
           "(:conditionTier IS NULL OR bl.conditionTier = :conditionTier) AND " +
           "(:frameMaterial IS NULL OR bl.frameMaterial = :frameMaterial) AND " +
           "bl.status = 'ACTIVE' AND bl.availabilityStatus = 'AVAILABLE' " +
           "ORDER BY bl.createdAt DESC")
    Page<BikeListing> findByMultipleCriteria(
            @Param("bikeType") BikeType bikeType,
            @Param("minPrice") BigDecimal minPrice,
            @Param("maxPrice") BigDecimal maxPrice,
            @Param("conditionTier") ConditionTier conditionTier,
            @Param("frameMaterial") FrameMaterial frameMaterial,
            Pageable pageable);

    /**
     * Finds listings by location (public city and state).
     */
    @Query("SELECT bl FROM BikeListing bl JOIN bl.location loc WHERE " +
           "LOWER(loc.publicCity) = LOWER(:city) AND LOWER(loc.publicState) = LOWER(:state) AND " +
           "bl.status = 'ACTIVE' AND bl.availabilityStatus = 'AVAILABLE' " +
           "ORDER BY bl.createdAt DESC")
    Page<BikeListing> findByLocation(@Param("city") String city, @Param("state") String state, Pageable pageable);

    /**
     * Counts active listings by seller.
     */
    @Query("SELECT COUNT(bl) FROM BikeListing bl WHERE bl.seller.id = :sellerId AND bl.status = 'ACTIVE'")
    long countActiveListingsBySellerId(@Param("sellerId") UUID sellerId);

    /**
     * Finds expired reservations that need to be released.
     */
    @Query("SELECT bl FROM BikeListing bl WHERE bl.availabilityStatus = 'RESERVED' AND bl.reservationExpiresAt < CURRENT_TIMESTAMP")
    List<BikeListing> findExpiredReservations();
}
