package com.bikeana.listing.domain;

/**
 * Enumeration for bike availability status.
 */
public enum AvailabilityStatus {
    AVAILABLE("Available"),
    RESERVED("Reserved"),
    SOLD("Sold"),
    UNAVAILABLE("Unavailable");

    private final String displayName;

    AvailabilityStatus(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }
}
