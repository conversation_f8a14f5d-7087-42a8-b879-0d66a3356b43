package com.bikeana.listing.api;

import com.bikeana.listing.domain.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO for bike listing summaries used in listing grids and search results.
 * Contains essential information without full details to optimize performance.
 */
public record BikeListingSummaryDto(
        UUID id,
        String title,
        String brand,
        String model,
        Integer year,
        BikeType bikeType,
        ConditionTier conditionTier,
        BigDecimal askingPrice,
        Boolean negotiable,
        String seoSlug,
        
        // Seller Information (minimal)
        BikeListingSellerSummaryDto seller,
        
        // Primary Photo
        BikeListingPhotoSummaryDto primaryPhoto,
        
        // Location (public only)
        String publicLocation,
        
        // Availability
        AvailabilityStatus availabilityStatus,
        
        // Metadata
        LocalDateTime createdAt
) {
    
    /**
     * Nested DTO for seller summary.
     */
    public record BikeListingSellerSummaryDto(
            UUID id,
            String fullName,
            String role
    ) {}
    
    /**
     * Nested DTO for photo summary.
     */
    public record BikeListingPhotoSummaryDto(
            UUID id,
            String fileName,
            String filePath,
            String altText
    ) {}
}
