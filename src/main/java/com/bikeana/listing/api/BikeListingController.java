package com.bikeana.listing.api;

import com.bikeana.listing.domain.*;
import com.bikeana.security.JwtAuthenticationFilter;
import com.bikeana.user.api.ErrorResponse;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * REST controller for bike listing operations.
 * Handles CRUD operations, search, and listing management.
 */
@RestController
@RequestMapping("/api/listings")
public class BikeListingController {

    private final BikeListingService bikeListingService;

    public BikeListingController(BikeListingService bikeListingService) {
        this.bikeListingService = bikeListingService;
    }

    /**
     * Creates a new bike listing.
     */
    @PostMapping
    public ResponseEntity<?> createListing(
            @Valid @RequestBody CreateBikeListingRequestDto request,
            Authentication authentication) {
        try {
            JwtAuthenticationFilter.UserPrincipal principal = 
                (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();
            
            BikeListingResponseDto listing = bikeListingService.createListing(request, principal.getUserId());
            return ResponseEntity.status(HttpStatus.CREATED).body(listing);
            
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("INVALID_REQUEST", e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(new ErrorResponse("LISTING_CREATION_FAILED", "Failed to create listing"));
        }
    }

    /**
     * Gets all active listings with pagination and sorting.
     */
    @GetMapping
    public ResponseEntity<Page<BikeListingSummaryDto>> getActiveListings(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort.Direction direction = sortDir.equalsIgnoreCase("desc") ? 
                Sort.Direction.DESC : Sort.Direction.ASC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
        
        Page<BikeListingSummaryDto> listings = bikeListingService.getActiveListings(pageable);
        return ResponseEntity.ok(listings);
    }

    /**
     * Gets a specific listing by ID.
     */
    @GetMapping("/{listingId}")
    public ResponseEntity<?> getListingById(
            @PathVariable UUID listingId,
            Authentication authentication) {
        
        UUID requestingUserId = null;
        if (authentication != null) {
            JwtAuthenticationFilter.UserPrincipal principal = 
                (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();
            requestingUserId = principal.getUserId();
        }
        
        Optional<BikeListingResponseDto> listing = bikeListingService.findListingById(listingId, requestingUserId);
        
        if (listing.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        
        return ResponseEntity.ok(listing.get());
    }

    /**
     * Gets a specific listing by SEO slug.
     */
    @GetMapping("/slug/{slug}")
    public ResponseEntity<?> getListingBySlug(
            @PathVariable String slug,
            Authentication authentication) {
        
        UUID requestingUserId = null;
        if (authentication != null) {
            JwtAuthenticationFilter.UserPrincipal principal = 
                (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();
            requestingUserId = principal.getUserId();
        }
        
        Optional<BikeListingResponseDto> listing = bikeListingService.findListingBySlug(slug, requestingUserId);
        
        if (listing.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        
        return ResponseEntity.ok(listing.get());
    }

    /**
     * Searches listings with multiple criteria.
     */
    @GetMapping("/search")
    public ResponseEntity<Page<BikeListingSummaryDto>> searchListings(
            @RequestParam(required = false) String q,
            @RequestParam(required = false) BikeType bikeType,
            @RequestParam(required = false) BigDecimal minPrice,
            @RequestParam(required = false) BigDecimal maxPrice,
            @RequestParam(required = false) ConditionTier conditionTier,
            @RequestParam(required = false) FrameMaterial frameMaterial,
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String state,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort.Direction direction = sortDir.equalsIgnoreCase("desc") ? 
                Sort.Direction.DESC : Sort.Direction.ASC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
        
        Page<BikeListingSummaryDto> listings = bikeListingService.searchListings(
                q, bikeType, minPrice, maxPrice, conditionTier, frameMaterial, city, state, pageable);
        
        return ResponseEntity.ok(listings);
    }

    /**
     * Gets listings by the current authenticated user.
     */
    @GetMapping("/my-listings")
    public ResponseEntity<?> getMyListings(Authentication authentication) {
        try {
            JwtAuthenticationFilter.UserPrincipal principal = 
                (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();
            
            List<BikeListingSummaryDto> listings = bikeListingService.getListingsBySeller(principal.getUserId());
            return ResponseEntity.ok(listings);
            
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(new ErrorResponse("FETCH_FAILED", "Failed to fetch listings"));
        }
    }

    /**
     * Updates listing status.
     */
    @PatchMapping("/{listingId}/status")
    public ResponseEntity<?> updateListingStatus(
            @PathVariable UUID listingId,
            @RequestBody UpdateListingStatusRequest request,
            Authentication authentication) {
        try {
            JwtAuthenticationFilter.UserPrincipal principal = 
                (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();
            
            Optional<BikeListingResponseDto> listing = bikeListingService.updateListingStatus(
                    listingId, request.status(), principal.getUserId());
            
            if (listing.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            
            return ResponseEntity.ok(listing.get());
            
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("INVALID_REQUEST", e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(new ErrorResponse("UPDATE_FAILED", "Failed to update listing status"));
        }
    }

    /**
     * Reserves a listing for purchase.
     */
    @PostMapping("/{listingId}/reserve")
    public ResponseEntity<?> reserveListing(
            @PathVariable UUID listingId,
            @RequestBody ReserveListingRequest request,
            Authentication authentication) {
        try {
            JwtAuthenticationFilter.UserPrincipal principal = 
                (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();
            
            Optional<BikeListingResponseDto> listing = bikeListingService.reserveListing(
                    listingId, principal.getUserId(), request.reservationHours());
            
            if (listing.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            
            return ResponseEntity.ok(listing.get());
            
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("INVALID_REQUEST", e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(new ErrorResponse("RESERVATION_FAILED", "Failed to reserve listing"));
        }
    }

    /**
     * Deletes a listing (soft delete).
     */
    @DeleteMapping("/{listingId}")
    public ResponseEntity<?> deleteListing(
            @PathVariable UUID listingId,
            Authentication authentication) {
        try {
            JwtAuthenticationFilter.UserPrincipal principal = 
                (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();
            
            boolean deleted = bikeListingService.deleteListing(listingId, principal.getUserId());
            
            if (!deleted) {
                return ResponseEntity.notFound().build();
            }
            
            return ResponseEntity.noContent().build();
            
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("INVALID_REQUEST", e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(new ErrorResponse("DELETE_FAILED", "Failed to delete listing"));
        }
    }

    /**
     * Request DTO for updating listing status.
     */
    public record UpdateListingStatusRequest(ListingStatus status) {}

    /**
     * Request DTO for reserving a listing.
     */
    public record ReserveListingRequest(int reservationHours) {}
}
