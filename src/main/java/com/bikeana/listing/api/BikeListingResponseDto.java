package com.bikeana.listing.api;

import com.bikeana.listing.domain.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * DTO for bike listing responses.
 * Contains all listing information formatted for API responses.
 */
public record BikeListingResponseDto(
        UUID id,
        String title,
        String description,
        ListingStatus status,
        
        // Bike Specifications
        String brand,
        String model,
        Integer year,
        FrameMaterial frameMaterial,
        String frameSize,
        BikeType bikeType,
        WheelSize wheelSize,
        Integer drivetrainSpeeds,
        String groupsetBrand,
        BrakeType brakeType,
        String color,
        
        // Condition & History
        ConditionTier conditionTier,
        String conditionNotes,
        String serviceHistory,
        String serialNumber,
        String warrantyInformation,
        
        // Pricing & Sales Terms
        BigDecimal askingPrice,
        Boolean negotiable,
        BigDecimal depositRequired,
        
        // Categorization & Search
        String tags,
        String seoSlug,
        
        // Seller Information
        BikeListingSellerDto seller,
        
        // Photos
        List<BikeListingPhotoDto> photos,
        BikeListingPhotoDto primaryPhoto,
        
        // Location (privacy-aware)
        BikeListingLocationDto location,
        
        // Availability
        AvailabilityStatus availabilityStatus,
        UUID reservedByUserId,
        LocalDateTime reservationExpiresAt,
        
        // Metadata
        LocalDateTime createdAt,
        LocalDateTime updatedAt
) {
    
    /**
     * Nested DTO for seller information.
     */
    public record BikeListingSellerDto(
            UUID id,
            String fullName,
            String role,
            String email, // Only shown to authenticated users
            String phone  // Only shown to authenticated users
    ) {}
    
    /**
     * Nested DTO for photo information.
     */
    public record BikeListingPhotoDto(
            UUID id,
            String fileName,
            String filePath,
            String contentType,
            Long fileSize,
            Boolean isPrimary,
            PhotoType photoType,
            String altText,
            Integer displayOrder,
            LocalDateTime createdAt
    ) {}
    
    /**
     * Nested DTO for location information (privacy-aware).
     */
    public record BikeListingLocationDto(
            UUID id,
            
            // Full address (only shown after escrow activation)
            String streetAddress,
            String addressLine2,
            String city,
            String state,
            String zipCode,
            String country,
            
            // Public information (always shown)
            String publicNeighborhood,
            String publicCity,
            String publicState,
            String publicDisplayAddress,
            
            // Additional info
            String pickupInstructions,
            Boolean isDefaultSellerAddress,
            
            LocalDateTime createdAt,
            LocalDateTime updatedAt
    ) {}
}
