package com.bikeana.listing.api;

import com.bikeana.listing.domain.*;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;

/**
 * DTO for creating a new bike listing.
 * Contains all required and optional fields with validation.
 */
public record CreateBikeListingRequestDto(
        
        // General Information
        @NotBlank(message = "Title is required")
        @Size(max = 200, message = "Title must not exceed 200 characters")
        String title,
        
        @NotBlank(message = "Description is required")
        @Size(max = 5000, message = "Description must not exceed 5000 characters")
        String description,
        
        // Bike Specifications
        @NotBlank(message = "Brand is required")
        @Size(max = 100, message = "Brand must not exceed 100 characters")
        String brand,
        
        @NotBlank(message = "Model is required")
        @Size(max = 100, message = "Model must not exceed 100 characters")
        String model,
        
        @NotNull(message = "Year is required")
        @Min(value = 1900, message = "Year must be 1900 or later")
        @Max(value = 2030, message = "Year must not be in the future")
        Integer year,
        
        @NotNull(message = "Frame material is required")
        FrameMaterial frameMaterial,
        
        @NotBlank(message = "Frame size is required")
        @Size(max = 50, message = "Frame size must not exceed 50 characters")
        String frameSize,
        
        @NotNull(message = "Bike type is required")
        BikeType bikeType,
        
        @NotNull(message = "Wheel size is required")
        WheelSize wheelSize,
        
        @NotNull(message = "Drivetrain speeds is required")
        @Min(value = 1, message = "Drivetrain speeds must be at least 1")
        @Max(value = 50, message = "Drivetrain speeds must not exceed 50")
        Integer drivetrainSpeeds,
        
        @NotBlank(message = "Groupset brand is required")
        @Size(max = 100, message = "Groupset brand must not exceed 100 characters")
        String groupsetBrand,
        
        @NotNull(message = "Brake type is required")
        BrakeType brakeType,
        
        @NotBlank(message = "Color is required")
        @Size(max = 50, message = "Color must not exceed 50 characters")
        String color,
        
        // Condition & History
        @NotNull(message = "Condition tier is required")
        ConditionTier conditionTier,
        
        @NotBlank(message = "Condition notes are required")
        @Size(max = 2000, message = "Condition notes must not exceed 2000 characters")
        String conditionNotes,
        
        @Size(max = 2000, message = "Service history must not exceed 2000 characters")
        String serviceHistory,
        
        @NotBlank(message = "Serial number is required")
        @Size(max = 100, message = "Serial number must not exceed 100 characters")
        String serialNumber,
        
        @Size(max = 1000, message = "Warranty information must not exceed 1000 characters")
        String warrantyInformation,
        
        // Pricing & Sales Terms
        @NotNull(message = "Asking price is required")
        @DecimalMin(value = "0.01", message = "Asking price must be greater than 0")
        @DecimalMax(value = "999999.99", message = "Asking price must not exceed $999,999.99")
        BigDecimal askingPrice,
        
        @NotNull(message = "Negotiable flag is required")
        Boolean negotiable,
        
        @DecimalMin(value = "0.00", message = "Deposit must not be negative")
        @DecimalMax(value = "99999.99", message = "Deposit must not exceed $99,999.99")
        BigDecimal depositRequired,
        
        // Categorization & Search
        @Size(max = 500, message = "Tags must not exceed 500 characters")
        String tags,
        
        // Location Information
        @NotNull(message = "Location information is required")
        CreateBikeListingLocationDto location
) {
    
    /**
     * Nested DTO for location information.
     */
    public record CreateBikeListingLocationDto(
            
            @NotBlank(message = "Street address is required")
            @Size(max = 200, message = "Street address must not exceed 200 characters")
            String streetAddress,
            
            @Size(max = 100, message = "Address line 2 must not exceed 100 characters")
            String addressLine2,
            
            @NotBlank(message = "City is required")
            @Size(max = 100, message = "City must not exceed 100 characters")
            String city,
            
            @NotBlank(message = "State is required")
            @Size(max = 50, message = "State must not exceed 50 characters")
            String state,
            
            @NotBlank(message = "ZIP code is required")
            @Pattern(regexp = "^\\d{5}(-\\d{4})?$", message = "ZIP code must be in format 12345 or 12345-6789")
            String zipCode,
            
            @NotBlank(message = "Public neighborhood is required")
            @Size(max = 100, message = "Public neighborhood must not exceed 100 characters")
            String publicNeighborhood,
            
            @Size(max = 1000, message = "Pickup instructions must not exceed 1000 characters")
            String pickupInstructions,
            
            Boolean isDefaultSellerAddress
    ) {}
}
