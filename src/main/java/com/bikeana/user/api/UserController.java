package com.bikeana.user.api;

import com.bikeana.security.JwtAuthenticationFilter;
import com.bikeana.user.domain.UserService;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * REST controller for user management operations.
 * Handles user profile operations for authenticated users.
 */
@RestController
@RequestMapping("/api/users")
public class UserController {

    private final UserService userService;

    public UserController(UserService userService) {
        this.userService = userService;
    }

    /**
     * Gets the current authenticated user's profile.
     * 
     * @param authentication The authentication object containing user details
     * @return UserResponseDto with user information
     */
    @GetMapping("/me")
    public ResponseEntity<?> getCurrentUser(Authentication authentication) {
        try {
            JwtAuthenticationFilter.UserPrincipal principal = 
                (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();
            
            Optional<UserResponseDto> userOpt = userService.findUserById(principal.getUserId());
            
            if (userOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            
            return ResponseEntity.ok(userOpt.get());
            
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(new ErrorResponse("PROFILE_FETCH_FAILED", "Failed to fetch user profile"));
        }
    }

    /**
     * Updates the current authenticated user's profile.
     * 
     * @param authentication The authentication object containing user details
     * @param updateRequest The profile update request
     * @return Updated UserResponseDto
     */
    @PutMapping("/me")
    public ResponseEntity<?> updateCurrentUser(
            Authentication authentication,
            @RequestBody UserProfileUpdateRequestDto updateRequest) {
        try {
            JwtAuthenticationFilter.UserPrincipal principal = 
                (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();
            
            Optional<UserResponseDto> userOpt = userService.updateUserProfile(
                    principal.getUserId(),
                    updateRequest.fullName(),
                    updateRequest.phone()
            );
            
            if (userOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            
            return ResponseEntity.ok(userOpt.get());
            
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(new ErrorResponse("PROFILE_UPDATE_FAILED", "Failed to update user profile"));
        }
    }

}
