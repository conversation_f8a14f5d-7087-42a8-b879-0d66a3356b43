package com.bikeana.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web configuration for static resources and file serving.
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Value("${app.upload.directory:uploads/listings}")
    private String uploadDirectory;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // Serve uploaded files
        registry.addResourceHandler("/uploads/**")
                .addResourceLocations("file:" + uploadDirectory + "/");
        
        // Serve static assets
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");
    }
}
