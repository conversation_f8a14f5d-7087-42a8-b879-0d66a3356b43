package com.bikeana.auth.api;

import com.bikeana.security.JwtService;
import com.bikeana.user.api.ErrorResponse;
import com.bikeana.user.api.SignupRequestDto;
import com.bikeana.user.api.UserResponseDto;
import com.bikeana.user.domain.User;
import com.bikeana.user.domain.UserService;
import io.jsonwebtoken.JwtException;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;
import java.util.UUID;

/**
 * REST controller for authentication operations.
 * Handles user registration, login, and token refresh.
 */
@RestController
@RequestMapping("/api/auth")
public class AuthController {

    private final UserService userService;
    private final JwtService jwtService;

    public AuthController(UserService userService, JwtService jwtService) {
        this.userService = userService;
        this.jwtService = jwtService;
    }

    /**
     * Registers a new user.
     * 
     * @param signupRequest The signup request containing user details
     * @return AuthResponseDto with tokens and user information
     */
    @PostMapping("/signup")
    public ResponseEntity<?> signup(@Valid @RequestBody SignupRequestDto signupRequest) {
        try {
            // Register the user
            UserResponseDto user = userService.registerUser(signupRequest);
            
            // Generate tokens
            String accessToken = jwtService.generateAccessToken(user.id(), user.email(), user.role());
            String refreshToken = jwtService.generateRefreshToken(user.id());
            
            // Return authentication response
            AuthResponseDto response = new AuthResponseDto(accessToken, refreshToken, user);
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
            
        } catch (UserService.UserAlreadyExistsException e) {
            return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body(new ErrorResponse("USER_ALREADY_EXISTS", e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("REGISTRATION_FAILED", "Failed to register user"));
        }
    }

    /**
     * Authenticates a user and returns JWT tokens.
     * 
     * @param loginRequest The login request containing email and password
     * @return AuthResponseDto with tokens and user information
     */
    @PostMapping("/login")
    public ResponseEntity<?> login(@Valid @RequestBody LoginRequestDto loginRequest) {
        try {
            // Authenticate user
            Optional<User> userOpt = userService.authenticateUser(loginRequest.email(), loginRequest.password());
            
            if (userOpt.isEmpty()) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(new ErrorResponse("INVALID_CREDENTIALS", "Invalid email or password"));
            }
            
            User user = userOpt.get();
            
            // Check if user is active
            if (!"ACTIVE".equals(user.getStatus())) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(new ErrorResponse("ACCOUNT_NOT_ACTIVE", "Account is not active"));
            }
            
            // Generate tokens
            String accessToken = jwtService.generateAccessToken(user.getId(), user.getEmail(), user.getRole());
            String refreshToken = jwtService.generateRefreshToken(user.getId());
            
            // Convert to response DTO
            UserResponseDto userResponse = new UserResponseDto(
                    user.getId(), user.getEmail(), user.getFullName(), 
                    user.getPhone(), user.getRole(), user.getStatus()
            );
            
            // Return authentication response
            AuthResponseDto response = new AuthResponseDto(accessToken, refreshToken, userResponse);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("LOGIN_FAILED", "Failed to authenticate user"));
        }
    }

    /**
     * Refreshes an access token using a refresh token.
     * 
     * @param refreshRequest The refresh token request
     * @return New access token
     */
    @PostMapping("/refresh")
    public ResponseEntity<?> refresh(@Valid @RequestBody RefreshTokenRequestDto refreshRequest) {
        try {
            // Validate refresh token
            UUID userId = jwtService.extractUserId(refreshRequest.refreshToken());
            
            // Check if token is expired
            if (jwtService.isTokenExpired(refreshRequest.refreshToken())) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(new ErrorResponse("TOKEN_EXPIRED", "Refresh token has expired"));
            }
            
            // Get user information
            Optional<UserResponseDto> userOpt = userService.findUserById(userId);
            if (userOpt.isEmpty()) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(new ErrorResponse("USER_NOT_FOUND", "User not found"));
            }
            
            UserResponseDto user = userOpt.get();
            
            // Generate new access token
            String newAccessToken = jwtService.generateAccessToken(user.id(), user.email(), user.role());
            
            // Return new tokens
            AuthResponseDto response = new AuthResponseDto(newAccessToken, refreshRequest.refreshToken(), user);
            return ResponseEntity.ok(response);
            
        } catch (JwtException e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(new ErrorResponse("INVALID_TOKEN", "Invalid refresh token"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("REFRESH_FAILED", "Failed to refresh token"));
        }
    }

}
