spring:
  datasource:
    url: ********************************************
    username: ${DB_USERNAME:bikeana_dev}
    password: ${DB_PASSWORD:dev_password}
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true

logging:
  level:
    com.bikeana: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# Security Configuration
app:
  security:
    jwt:
      secret: ${JWT_SECRET:bikeana-dev-secret-key-that-should-be-changed-in-production-and-be-at-least-256-bits-long-for-security}
      access-token-expiration-minutes: 15
      refresh-token-expiration-days: 7
  upload:
    directory: uploads/listings
  base-url: http://localhost:8080
