{#include layout.qute.html}
    {#title}Create Listing - Bikeana{/title}
    {#content}
        <div class="min-h-screen bg-gray-50 py-8">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900">List Your Bike</h1>
                    <p class="mt-2 text-gray-600">Create a detailed listing to attract potential buyers</p>
                </div>

                <!-- Alert Container -->
                <div id="alert-container" class="mb-6"></div>

                <!-- Form -->
                <div class="bg-white shadow rounded-lg">
                    <form hx-post="/htmx/listings/create" 
                          hx-target="#alert-container" 
                          hx-swap="innerHTML"
                          class="space-y-8 divide-y divide-gray-200 p-6">
                        
                        <!-- Basic Information -->
                        <div class="space-y-6">
                            <div>
                                <h3 class="text-lg leading-6 font-medium text-gray-900">Basic Information</h3>
                                <p class="mt-1 text-sm text-gray-500">Tell buyers about your bike</p>
                            </div>

                            <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                <div class="sm:col-span-6">
                                    <label for="title" class="block text-sm font-medium text-gray-700">Listing Title *</label>
                                    <input type="text" 
                                           name="title" 
                                           id="title" 
                                           required
                                           placeholder="e.g., 2022 Trek Domane SL 5 - Excellent Condition"
                                           class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                </div>

                                <div class="sm:col-span-6">
                                    <label for="description" class="block text-sm font-medium text-gray-700">Description *</label>
                                    <textarea name="description" 
                                              id="description" 
                                              rows="4" 
                                              required
                                              placeholder="Describe your bike's features, condition, and any upgrades..."
                                              class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Bike Specifications -->
                        <div class="pt-8 space-y-6">
                            <div>
                                <h3 class="text-lg leading-6 font-medium text-gray-900">Bike Specifications</h3>
                                <p class="mt-1 text-sm text-gray-500">Provide detailed specifications</p>
                            </div>

                            <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                <div class="sm:col-span-2">
                                    <label for="brand" class="block text-sm font-medium text-gray-700">Brand *</label>
                                    <input type="text" 
                                           name="brand" 
                                           id="brand" 
                                           required
                                           placeholder="e.g., Trek, Specialized, Giant"
                                           class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                </div>

                                <div class="sm:col-span-2">
                                    <label for="model" class="block text-sm font-medium text-gray-700">Model *</label>
                                    <input type="text" 
                                           name="model" 
                                           id="model" 
                                           required
                                           placeholder="e.g., Domane SL 5"
                                           class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                </div>

                                <div class="sm:col-span-2">
                                    <label for="year" class="block text-sm font-medium text-gray-700">Year *</label>
                                    <input type="number" 
                                           name="year" 
                                           id="year" 
                                           required
                                           min="1900"
                                           max="2030"
                                           placeholder="2022"
                                           class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                </div>

                                <div class="sm:col-span-2">
                                    <label for="bikeType" class="block text-sm font-medium text-gray-700">Bike Type *</label>
                                    <select name="bikeType" 
                                            id="bikeType" 
                                            required
                                            class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="">Select bike type</option>
                                        {#for bikeType in bikeTypes}
                                            <option value="{bikeType}">{bikeType.displayName}</option>
                                        {/for}
                                    </select>
                                </div>

                                <div class="sm:col-span-2">
                                    <label for="frameMaterial" class="block text-sm font-medium text-gray-700">Frame Material *</label>
                                    <select name="frameMaterial" 
                                            id="frameMaterial" 
                                            required
                                            class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="">Select material</option>
                                        {#for material in frameMaterials}
                                            <option value="{material}">{material.displayName}</option>
                                        {/for}
                                    </select>
                                </div>

                                <div class="sm:col-span-2">
                                    <label for="frameSize" class="block text-sm font-medium text-gray-700">Frame Size *</label>
                                    <input type="text"
                                           name="frameSize"
                                           id="frameSize"
                                           required
                                           placeholder="e.g., 56cm, Large, 18\""
                                           class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                </div>

                                <div class="sm:col-span-2">
                                    <label for="wheelSize" class="block text-sm font-medium text-gray-700">Wheel Size *</label>
                                    <select name="wheelSize"
                                            id="wheelSize"
                                            required
                                            class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="">Select wheel size</option>
                                        {#for wheelSize in wheelSizes}
                                            <option value="{wheelSize}">{wheelSize.displayName}</option>
                                        {/for}
                                    </select>
                                </div>

                                <div class="sm:col-span-2">
                                    <label for="drivetrainSpeeds" class="block text-sm font-medium text-gray-700">Drivetrain Speeds *</label>
                                    <input type="number"
                                           name="drivetrainSpeeds"
                                           id="drivetrainSpeeds"
                                           required
                                           min="1"
                                           max="50"
                                           placeholder="11"
                                           class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                </div>

                                <div class="sm:col-span-2">
                                    <label for="groupsetBrand" class="block text-sm font-medium text-gray-700">Groupset Brand *</label>
                                    <input type="text"
                                           name="groupsetBrand"
                                           id="groupsetBrand"
                                           required
                                           placeholder="e.g., Shimano 105, SRAM Rival"
                                           class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                </div>

                                <div class="sm:col-span-2">
                                    <label for="brakeType" class="block text-sm font-medium text-gray-700">Brake Type *</label>
                                    <select name="brakeType"
                                            id="brakeType"
                                            required
                                            class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="">Select brake type</option>
                                        {#for brakeType in brakeTypes}
                                            <option value="{brakeType}">{brakeType.displayName}</option>
                                        {/for}
                                    </select>
                                </div>

                                <div class="sm:col-span-2">
                                    <label for="color" class="block text-sm font-medium text-gray-700">Color *</label>
                                    <input type="text"
                                           name="color"
                                           id="color"
                                           required
                                           placeholder="e.g., Matte Black, Red"
                                           class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                </div>
                            </div>
                        </div>

                        <!-- Condition & Pricing -->
                        <div class="pt-8 space-y-6">
                            <div>
                                <h3 class="text-lg leading-6 font-medium text-gray-900">Condition & Pricing</h3>
                                <p class="mt-1 text-sm text-gray-500">Describe condition and set your price</p>
                            </div>

                            <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                <div class="sm:col-span-3">
                                    <label for="conditionTier" class="block text-sm font-medium text-gray-700">Condition *</label>
                                    <select name="conditionTier" 
                                            id="conditionTier" 
                                            required
                                            class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="">Select condition</option>
                                        {#for condition in conditionTiers}
                                            <option value="{condition}">{condition.displayName}</option>
                                        {/for}
                                    </select>
                                </div>

                                <div class="sm:col-span-3">
                                    <label for="askingPrice" class="block text-sm font-medium text-gray-700">Asking Price *</label>
                                    <div class="mt-1 relative rounded-md shadow-sm">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <span class="text-gray-500 sm:text-sm">$</span>
                                        </div>
                                        <input type="number" 
                                               name="askingPrice" 
                                               id="askingPrice" 
                                               required
                                               min="0.01"
                                               step="0.01"
                                               placeholder="2500.00"
                                               class="focus:ring-blue-500 focus:border-blue-500 block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md">
                                    </div>
                                </div>

                                <div class="sm:col-span-6">
                                    <label for="conditionNotes" class="block text-sm font-medium text-gray-700">Condition Notes *</label>
                                    <textarea name="conditionNotes" 
                                              id="conditionNotes" 
                                              rows="3" 
                                              required
                                              placeholder="Describe the bike's condition, any wear, scratches, or maintenance needed..."
                                              class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"></textarea>
                                </div>

                                <div class="sm:col-span-6">
                                    <label for="serialNumber" class="block text-sm font-medium text-gray-700">Serial Number *</label>
                                    <input type="text"
                                           name="serialNumber"
                                           id="serialNumber"
                                           required
                                           placeholder="Usually found on the bottom bracket"
                                           class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                </div>

                                <div class="sm:col-span-6">
                                    <label for="serviceHistory" class="block text-sm font-medium text-gray-700">Service History (Optional)</label>
                                    <textarea name="serviceHistory"
                                              id="serviceHistory"
                                              rows="3"
                                              placeholder="Recent maintenance, tune-ups, part replacements..."
                                              class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"></textarea>
                                </div>

                                <div class="sm:col-span-6">
                                    <label for="warrantyInformation" class="block text-sm font-medium text-gray-700">Warranty Information (Optional)</label>
                                    <textarea name="warrantyInformation"
                                              id="warrantyInformation"
                                              rows="2"
                                              placeholder="Remaining warranty details..."
                                              class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"></textarea>
                                </div>

                                <div class="sm:col-span-3">
                                    <label for="depositRequired" class="block text-sm font-medium text-gray-700">Deposit Required (Optional)</label>
                                    <div class="mt-1 relative rounded-md shadow-sm">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <span class="text-gray-500 sm:text-sm">$</span>
                                        </div>
                                        <input type="number"
                                               name="depositRequired"
                                               id="depositRequired"
                                               min="0.00"
                                               step="0.01"
                                               placeholder="0.00"
                                               class="focus:ring-blue-500 focus:border-blue-500 block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md">
                                    </div>
                                </div>

                                <div class="sm:col-span-3">
                                    <div class="flex items-center">
                                        <input id="negotiable"
                                               name="negotiable"
                                               type="checkbox"
                                               value="true"
                                               class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                        <label for="negotiable" class="ml-2 block text-sm text-gray-900">
                                            Price is negotiable
                                        </label>
                                    </div>
                                </div>

                                <div class="sm:col-span-6">
                                    <label for="tags" class="block text-sm font-medium text-gray-700">Tags/Keywords (Optional)</label>
                                    <input type="text"
                                           name="tags"
                                           id="tags"
                                           placeholder="e.g., road bike, carbon, shimano 105, endurance"
                                           class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                    <p class="mt-2 text-sm text-gray-500">Separate tags with commas to help buyers find your listing</p>
                                </div>
                            </div>
                        </div>

                        <!-- Location -->
                        <div class="pt-8 space-y-6">
                            <div>
                                <h3 class="text-lg leading-6 font-medium text-gray-900">Pickup Location</h3>
                                <p class="mt-1 text-sm text-gray-500">
                                    <span class="text-yellow-600 font-medium">Privacy Notice:</span> 
                                    Only the general area will be shown publicly. Full address is revealed after payment escrow.
                                </p>
                            </div>

                            <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                <div class="sm:col-span-6">
                                    <label for="location.streetAddress" class="block text-sm font-medium text-gray-700">Street Address *</label>
                                    <input type="text" 
                                           name="location.streetAddress" 
                                           id="location.streetAddress" 
                                           required
                                           placeholder="123 Main Street"
                                           class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                </div>

                                <div class="sm:col-span-2">
                                    <label for="location.city" class="block text-sm font-medium text-gray-700">City *</label>
                                    <input type="text" 
                                           name="location.city" 
                                           id="location.city" 
                                           required
                                           placeholder="Charlotte"
                                           class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                </div>

                                <div class="sm:col-span-2">
                                    <label for="location.state" class="block text-sm font-medium text-gray-700">State *</label>
                                    <input type="text" 
                                           name="location.state" 
                                           id="location.state" 
                                           required
                                           placeholder="NC"
                                           class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                </div>

                                <div class="sm:col-span-2">
                                    <label for="location.zipCode" class="block text-sm font-medium text-gray-700">ZIP Code *</label>
                                    <input type="text"
                                           name="location.zipCode"
                                           id="location.zipCode"
                                           required
                                           pattern="[0-9]{5}(-[0-9]{4})?"
                                           placeholder="28277"
                                           class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                </div>

                                <div class="sm:col-span-6">
                                    <label for="location.publicNeighborhood" class="block text-sm font-medium text-gray-700">Public Neighborhood/Area *</label>
                                    <input type="text"
                                           name="location.publicNeighborhood"
                                           id="location.publicNeighborhood"
                                           required
                                           placeholder="e.g., SouthPark, Uptown, University Area"
                                           class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                    <p class="mt-2 text-sm text-gray-500">This is what buyers will see before purchase</p>
                                </div>

                                <div class="sm:col-span-6">
                                    <label for="location.pickupInstructions" class="block text-sm font-medium text-gray-700">Pickup Instructions (Optional)</label>
                                    <textarea name="location.pickupInstructions"
                                              id="location.pickupInstructions"
                                              rows="3"
                                              placeholder="Special instructions for pickup (e.g., available weekdays after 5pm, call ahead, etc.)"
                                              class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Submit -->
                        <div class="pt-8">
                            <div class="flex justify-end">
                                <button type="button" 
                                        onclick="window.history.back()"
                                        class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    Cancel
                                </button>
                                <button type="submit" 
                                        class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    Create Listing
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Photo Upload Notice -->
                <div class="mt-6 bg-blue-50 border border-blue-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-800">Photo Upload</h3>
                            <div class="mt-2 text-sm text-blue-700">
                                <p>After creating your listing, you'll be able to upload photos. We require a minimum of 5 high-quality photos including drive-side, non-drive-side, drivetrain, brake, and frame detail shots.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {/content}
{/include}
