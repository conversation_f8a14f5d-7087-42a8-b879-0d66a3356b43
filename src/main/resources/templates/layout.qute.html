<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title ?: 'Bikeana - Bike Marketplace'}</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>

    <!-- Alpine.js for additional interactivity -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Custom styles -->
    <style>
        .htmx-indicator {
            opacity: 0;
            transition: opacity 200ms ease-in;
        }
        .htmx-request .htmx-indicator {
            opacity: 1;
        }
        .htmx-request.htmx-indicator {
            opacity: 1;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="flex items-center">
                        <svg class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                        <span class="ml-2 text-xl font-bold text-gray-900">Bikeana</span>
                    </a>
                </div>

                <div class="flex items-center space-x-4">
                    <a href="/listings" class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                        Browse Bikes
                    </a>
                    {#if isAuthenticated}
                        <a href="/my-listings" class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                            My Listings
                        </a>
                        <a href="/dashboard" class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                            Dashboard
                        </a>
                        <a href="/profile" class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                            Profile
                        </a>
                        <button
                            hx-post="/htmx/auth/logout"
                            hx-target="#alerts"
                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            Logout
                        </button>
                    {#else}
                        <a href="/login" class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                            Login
                        </a>
                        <a href="/signup" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            Sign Up
                        </a>
                    {/if}
                </div>
            </div>
        </div>
    </nav>

    <!-- Alerts Container -->
    <div id="alerts" class="fixed top-20 right-4 z-50 space-y-2"></div>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {#insert content}
            <div class="px-4 py-6 sm:px-0">
                <div class="border-4 border-dashed border-gray-200 rounded-lg h-96 flex items-center justify-center">
                    <p class="text-gray-500">Content goes here</p>
                </div>
            </div>
        {/insert}
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t mt-auto">
        <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <p class="text-gray-500 text-sm">
                    © {currentYear ?: 2025} Bikeana. All rights reserved.
                </p>
                <div class="flex space-x-6">
                    <a href="#" class="text-gray-400 hover:text-gray-500">
                        <span class="sr-only">Privacy</span>
                        Privacy
                    </a>
                    <a href="#" class="text-gray-400 hover:text-gray-500">
                        <span class="sr-only">Terms</span>
                        Terms
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- HTMX Configuration -->
    <script>
        // Configure HTMX
        document.body.addEventListener('htmx:configRequest', function(evt) {
            // Add CSRF token if needed
            // evt.detail.headers['X-CSRF-Token'] = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        });

        // Handle HTMX errors
        document.body.addEventListener('htmx:responseError', function(evt) {
            console.error('HTMX Error:', evt.detail);
            // Show error message
            const alertsContainer = document.getElementById('alerts');
            const errorAlert = document.createElement('div');
            errorAlert.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4';
            errorAlert.innerHTML = '<strong>Error:</strong> Something went wrong. Please try again.';
            alertsContainer.appendChild(errorAlert);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                errorAlert.remove();
            }, 5000);
        });

        // Auto-remove success alerts
        document.body.addEventListener('htmx:afterSwap', function(evt) {
            if (evt.target.id === 'alerts') {
                const alerts = evt.target.querySelectorAll('.bg-green-100, .bg-blue-100');
                alerts.forEach(alert => {
                    setTimeout(() => {
                        alert.style.opacity = '0';
                        setTimeout(() => alert.remove(), 300);
                    }, 3000);
                });
            }
        });
    </script>
</body>
</html>
