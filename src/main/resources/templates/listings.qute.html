{#include layout.qute.html}
    {#title}Browse Bikes - Bikeana{/title}
    {#content}
        <div class="min-h-screen bg-gray-50">
            <!-- Header Section -->
            <div class="bg-white shadow-sm border-b">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">Browse Bikes</h1>
                            <p class="mt-2 text-gray-600">Find your perfect ride from our marketplace</p>
                        </div>
                        {#if isAuthenticated}
                            <div class="mt-4 lg:mt-0">
                                <a href="/listings/create" 
                                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                    List Your Bike
                                </a>
                            </div>
                        {/if}
                    </div>
                </div>
            </div>

            <!-- Search and Filters Section -->
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="bg-white rounded-lg shadow p-6 mb-6">
                    <form method="GET" action="/listings" class="space-y-4">
                        <!-- Search Bar -->
                        <div>
                            <label for="q" class="block text-sm font-medium text-gray-700">Search</label>
                            <input type="text" 
                                   id="q" 
                                   name="q" 
                                   value="{searchTerm ?: ''}"
                                   placeholder="Search by brand, model, or description..."
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <!-- Filter Row -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <!-- Bike Type Filter -->
                            <div>
                                <label for="bikeType" class="block text-sm font-medium text-gray-700">Bike Type</label>
                                <select id="bikeType" name="bikeType" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">All Types</option>
                                    {#for bikeType in bikeTypes}
                                        <option value="{bikeType}" {#if selectedBikeType == bikeType}selected{/if}>
                                            {bikeType.displayName}
                                        </option>
                                    {/for}
                                </select>
                            </div>

                            <!-- Condition Filter -->
                            <div>
                                <label for="conditionTier" class="block text-sm font-medium text-gray-700">Condition</label>
                                <select id="conditionTier" name="conditionTier" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">All Conditions</option>
                                    {#for condition in conditionTiers}
                                        <option value="{condition}" {#if selectedCondition == condition}selected{/if}>
                                            {condition.displayName}
                                        </option>
                                    {/for}
                                </select>
                            </div>

                            <!-- Price Range -->
                            <div>
                                <label for="minPrice" class="block text-sm font-medium text-gray-700">Min Price</label>
                                <input type="number" 
                                       id="minPrice" 
                                       name="minPrice" 
                                       value="{minPrice ?: ''}"
                                       placeholder="$0"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            </div>

                            <div>
                                <label for="maxPrice" class="block text-sm font-medium text-gray-700">Max Price</label>
                                <input type="number" 
                                       id="maxPrice" 
                                       name="maxPrice" 
                                       value="{maxPrice ?: ''}"
                                       placeholder="$10,000"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end">
                            <button type="submit" 
                                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                                Search
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Results Section -->
                <div class="mb-6">
                    <p class="text-gray-600">
                        Showing {listings.numberOfElements} of {listings.totalElements} bikes
                        {#if searchTerm}for "{searchTerm}"{/if}
                    </p>
                </div>

                <!-- Listings Grid -->
                {#if listings.content.size() > 0}
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
                        {#for listing in listings.content}
                            <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200">
                                <!-- Photo -->
                                <div class="aspect-w-16 aspect-h-12 bg-gray-200">
                                    {#if listing.primaryPhoto()}
                                        <img src="/uploads/{listing.primaryPhoto().filePath()}"
                                             alt="{listing.primaryPhoto().altText() ?: listing.title()}"
                                             class="w-full h-48 object-cover">
                                    {#else}
                                        <div class="w-full h-48 bg-gray-300 flex items-center justify-center">
                                            <svg class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                    {/if}
                                </div>

                                <!-- Content -->
                                <div class="p-4">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                        <a href="/listings/{listing.seoSlug()}" class="hover:text-blue-600">
                                            {listing.title()}
                                        </a>
                                    </h3>

                                    <p class="text-sm text-gray-600 mb-2">
                                        {listing.year()} {listing.brand()} {listing.model()}
                                    </p>

                                    <div class="flex items-center justify-between mb-2">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            {listing.bikeType().displayName()}
                                        </span>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            {listing.conditionTier().displayName()}
                                        </span>
                                    </div>

                                    <div class="flex items-center justify-between">
                                        <div>
                                            <span class="text-2xl font-bold text-gray-900">${listing.askingPrice()}</span>
                                            {#if listing.negotiable()}
                                                <span class="text-sm text-gray-500">OBO</span>
                                            {/if}
                                        </div>
                                        <span class="text-sm text-gray-500">{listing.publicLocation()}</span>
                                    </div>
                                </div>
                            </div>
                        {/for}
                    </div>

                    <!-- Pagination -->
                    {#if totalPages > 1}
                        <div class="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
                            <div class="flex flex-1 justify-between sm:hidden">
                                {#if currentPage > 0}
                                    <a href="?page={currentPage - 1}" class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">Previous</a>
                                {/if}
                                {#if currentPage < totalPages - 1}
                                    <a href="?page={currentPage + 1}" class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">Next</a>
                                {/if}
                            </div>
                            <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                                <div>
                                    <p class="text-sm text-gray-700">
                                        Showing page <span class="font-medium">{currentPage + 1}</span> of <span class="font-medium">{totalPages}</span>
                                    </p>
                                </div>
                                <div>
                                    <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                                        {#if currentPage > 0}
                                            <a href="?page={currentPage - 1}" class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                                <span class="sr-only">Previous</span>
                                                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                    <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
                                                </svg>
                                            </a>
                                        {/if}
                                        
                                        {#for i in 0..totalPages-1}
                                            {#if i == currentPage}
                                                <span class="relative z-10 inline-flex items-center bg-blue-600 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">{i + 1}</span>
                                            {#else}
                                                <a href="?page={i}" class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">{i + 1}</a>
                                            {/if}
                                        {/for}
                                        
                                        {#if currentPage < totalPages - 1}
                                            <a href="?page={currentPage + 1}" class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                                <span class="sr-only">Next</span>
                                                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                    <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                                                </svg>
                                            </a>
                                        {/if}
                                    </nav>
                                </div>
                            </div>
                        </div>
                    {/if}
                {#else}
                    <!-- No Results -->
                    <div class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No bikes found</h3>
                        <p class="mt-1 text-sm text-gray-500">Try adjusting your search criteria or browse all listings.</p>
                        <div class="mt-6">
                            <a href="/listings" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                View All Bikes
                            </a>
                        </div>
                    </div>
                {/if}
            </div>
        </div>
    {/content}
{/include}
