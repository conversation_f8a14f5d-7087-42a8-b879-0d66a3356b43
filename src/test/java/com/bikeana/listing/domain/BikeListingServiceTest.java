package com.bikeana.listing.domain;

import com.bikeana.listing.api.BikeListingResponseDto;
import com.bikeana.listing.api.CreateBikeListingRequestDto;
import com.bikeana.user.domain.User;
import com.bikeana.user.domain.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.math.BigDecimal;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@SpringBootTest
@ActiveProfiles("test")
@Testcontainers
@Transactional
class BikeListingServiceTest {

    @Autowired
    private BikeListingService bikeListingService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private BikeListingRepository bikeListingRepository;

    private User testSeller;

    @BeforeEach
    void setUp() {
        // Create a test seller
        testSeller = new User();
        testSeller.setRole("PRIVATE_SELLER");
        testSeller.setFullName("Test Seller");
        testSeller.setEmail("<EMAIL>");
        testSeller.setPasswordHash("hashedpassword");
        testSeller.setPhone("555-1234");
        testSeller.setStatus("ACTIVE");
        testSeller = userRepository.save(testSeller);
    }

    @Test
    void createListing_WithValidData_ShouldCreateListing() {
        // Given
        CreateBikeListingRequestDto request = createValidListingRequest();

        // When
        BikeListingResponseDto result = bikeListingService.createListing(request, testSeller.getId());

        // Then
        assertThat(result).isNotNull();
        assertThat(result.title()).isEqualTo("2022 Trek Domane SL 5");
        assertThat(result.brand()).isEqualTo("Trek");
        assertThat(result.model()).isEqualTo("Domane SL 5");
        assertThat(result.year()).isEqualTo(2022);
        assertThat(result.askingPrice()).isEqualTo(new BigDecimal("2500.00"));
        assertThat(result.status()).isEqualTo(ListingStatus.ACTIVE);
        assertThat(result.availabilityStatus()).isEqualTo(AvailabilityStatus.AVAILABLE);
        assertThat(result.seoSlug()).isNotBlank();
        assertThat(result.seller().id()).isEqualTo(testSeller.getId());
    }

    @Test
    void createListing_WithDuplicateSerialNumber_ShouldThrowException() {
        // Given
        CreateBikeListingRequestDto request1 = createValidListingRequest();
        CreateBikeListingRequestDto request2 = createValidListingRequest();

        // When
        bikeListingService.createListing(request1, testSeller.getId());

        // Then
        assertThatThrownBy(() -> bikeListingService.createListing(request2, testSeller.getId()))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("serial number already exists");
    }

    @Test
    void createListing_WithInvalidUserRole_ShouldThrowException() {
        // Given
        User buyer = new User();
        buyer.setRole("BUYER");
        buyer.setFullName("Test Buyer");
        buyer.setEmail("<EMAIL>");
        buyer.setPasswordHash("hashedpassword");
        buyer.setStatus("ACTIVE");
        buyer = userRepository.save(buyer);

        CreateBikeListingRequestDto request = createValidListingRequest();

        // When & Then
        assertThatThrownBy(() -> bikeListingService.createListing(request, buyer.getId()))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("does not have permission to create listings");
    }

    @Test
    void findListingById_WithValidId_ShouldReturnListing() {
        // Given
        CreateBikeListingRequestDto request = createValidListingRequest();
        BikeListingResponseDto created = bikeListingService.createListing(request, testSeller.getId());

        // When
        Optional<BikeListingResponseDto> result = bikeListingService.findListingById(created.id(), null);

        // Then
        assertThat(result).isPresent();
        assertThat(result.get().id()).isEqualTo(created.id());
        assertThat(result.get().title()).isEqualTo(created.title());
    }

    @Test
    void findListingBySlug_WithValidSlug_ShouldReturnListing() {
        // Given
        CreateBikeListingRequestDto request = createValidListingRequest();
        BikeListingResponseDto created = bikeListingService.createListing(request, testSeller.getId());

        // When
        Optional<BikeListingResponseDto> result = bikeListingService.findListingBySlug(created.seoSlug(), null);

        // Then
        assertThat(result).isPresent();
        assertThat(result.get().seoSlug()).isEqualTo(created.seoSlug());
    }

    @Test
    void updateListingStatus_WithValidData_ShouldUpdateStatus() {
        // Given
        CreateBikeListingRequestDto request = createValidListingRequest();
        BikeListingResponseDto created = bikeListingService.createListing(request, testSeller.getId());

        // When
        Optional<BikeListingResponseDto> result = bikeListingService.updateListingStatus(
                created.id(), ListingStatus.SOLD, testSeller.getId());

        // Then
        assertThat(result).isPresent();
        assertThat(result.get().status()).isEqualTo(ListingStatus.SOLD);
        assertThat(result.get().availabilityStatus()).isEqualTo(AvailabilityStatus.SOLD);
    }

    @Test
    void reserveListing_WithValidData_ShouldReserveListing() {
        // Given
        User buyer = new User();
        buyer.setRole("BUYER");
        buyer.setFullName("Test Buyer");
        buyer.setEmail("<EMAIL>");
        buyer.setPasswordHash("hashedpassword");
        buyer.setStatus("ACTIVE");
        buyer = userRepository.save(buyer);

        CreateBikeListingRequestDto request = createValidListingRequest();
        BikeListingResponseDto created = bikeListingService.createListing(request, testSeller.getId());

        // When
        Optional<BikeListingResponseDto> result = bikeListingService.reserveListing(
                created.id(), buyer.getId(), 24);

        // Then
        assertThat(result).isPresent();
        assertThat(result.get().availabilityStatus()).isEqualTo(AvailabilityStatus.RESERVED);
        assertThat(result.get().reservedByUserId()).isEqualTo(buyer.getId());
        assertThat(result.get().reservationExpiresAt()).isNotNull();
    }

    @Test
    void reserveListing_SellerCannotReserveOwnListing_ShouldThrowException() {
        // Given
        CreateBikeListingRequestDto request = createValidListingRequest();
        BikeListingResponseDto created = bikeListingService.createListing(request, testSeller.getId());

        // When & Then
        assertThatThrownBy(() -> bikeListingService.reserveListing(created.id(), testSeller.getId(), 24))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("Seller cannot reserve their own listing");
    }

    private CreateBikeListingRequestDto createValidListingRequest() {
        CreateBikeListingRequestDto.CreateBikeListingLocationDto location = 
                new CreateBikeListingRequestDto.CreateBikeListingLocationDto(
                        "123 Main St",
                        null,
                        "Charlotte",
                        "NC",
                        "28202",
                        "SouthPark",
                        "Available for pickup weekdays after 5pm",
                        true
                );

        return new CreateBikeListingRequestDto(
                "2022 Trek Domane SL 5",
                "Excellent condition road bike with carbon frame. Recently serviced.",
                "Trek",
                "Domane SL 5",
                2022,
                FrameMaterial.CARBON,
                "56cm",
                BikeType.ROAD,
                WheelSize.SEVEN_HUNDRED_C,
                22,
                "Shimano 105",
                BrakeType.HYDRAULIC_DISC,
                "Matte Black",
                ConditionTier.EXCELLENT,
                "Minor scuffs on the frame, everything works perfectly",
                "Recently tuned up at local bike shop",
                "ABC123456789",
                "2 years remaining on frame warranty",
                new BigDecimal("2500.00"),
                true,
                null,
                "road bike, carbon, shimano 105",
                location
        );
    }
}
