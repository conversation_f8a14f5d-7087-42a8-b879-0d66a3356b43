# Bikeana Marketplace

## Problem
Finding a good quality used bike online (Facebook marketplace, Craigslist) **locally**, is really cumbersome and time consuming.

## Solution
An online marketplace for bikes with good quality photos and descriptions where users can trade their bikes locally, through auctions and classified modes.

## Technology Stack

- **Spring Boot**: 3.5.0 (Latest stable)
- **Java**: 21 (LTS)
- **Database**: PostgreSQL with JDBC Driver 42.7.5
- **Build Tool**: Maven
- **Testing**: JUnit 5 with Testcontainers (PostgreSQL)
- **Frontend**: Qute templating engine with HTMX for dynamic interactions
- **Styling**: Tailwind CSS for responsive design
- **Authentication**: JWT-based authentication with HTTP-only cookies
- **Security**: Spring Security with custom JWT filter

## Prerequisites

- Java 21 or higher
- Maven 3.6+
- Docker runtime (Docker Desktop, Rancher Desktop, Podman, Colima, etc.)
- PostgreSQL 12+ (optional - can use Docker container instead)

## Quick Start

### Database Setup
Create a PostgreSQL database for development:

```sql
CREATE DATABASE bikeana_dev;
CREATE USER bikeana_dev WITH PASSWORD 'dev_password';
GRANT ALL PRIVILEGES ON DATABASE bikeana_dev TO bikeana_dev;
```

### Running the Application

#### Option 1: Quick Start with Development Script (Recommended)
```bash
# Start PostgreSQL container and Spring Boot application
./scripts/start-dev.sh
```

This script will:
- 🐳 Start PostgreSQL container with Docker Compose
- ⏳ Wait for database to be ready
- 🌱 Start Spring Boot application with dev profile
- 📊 Provide pgAdmin access at http://localhost:8081

#### Option 2: Manual Setup
```bash
# Start PostgreSQL container
docker-compose -f docker-compose.dev.yml up -d postgres

# Set environment variables
export DB_USERNAME=bikeana_dev
export DB_PASSWORD=dev_password

# Start Spring Boot application
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

#### Option 3: Use External PostgreSQL
If you have PostgreSQL running locally or elsewhere:
```bash
# Set your database credentials
export DB_USERNAME=your_username
export DB_PASSWORD=your_password

# Start the application
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

The application will start on `http://localhost:8080` with the `dev` profile active.

#### Development Tools
- **Application**: http://localhost:8080
- **pgAdmin**: http://localhost:8081 (<EMAIL> / admin123)
- **PostgreSQL**: localhost:5433 (bikeana_dev / dev_password)

### Using the Application

1. **Visit the Application**: Navigate to `http://localhost:8080`
2. **Register a New Account**:
   - Click "Sign Up"
   - Fill in your details (Full Name, Email, Phone, Password)
   - Choose account type (Private Seller or Dealer)
3. **Login**: Use your email and password to sign in
4. **Explore Features**:
   - **Dashboard**: View your account overview
   - **Profile**: Manage your personal information
   - **Navigation**: Use the top navigation bar to access different sections

The application features a modern, responsive interface with:
- Clean authentication flow
- Dynamic content updates using HTMX
- Secure JWT-based session management
- Professional UI with Tailwind CSS

### Stopping the Development Environment
```bash
# Stop the Spring Boot application (Ctrl+C in the terminal)
# Then stop the PostgreSQL container
docker-compose -f docker-compose.dev.yml down

# Or stop everything including volumes (removes all data)
docker-compose -f docker-compose.dev.yml down -v
```

### Testing
```bash
mvn test
```

Tests use Testcontainers to spin up a real PostgreSQL database in Docker.

## Features

### 🔐 Authentication & Security
- **JWT-based Authentication**: Secure authentication using JSON Web Tokens
- **HTTP-only Cookies**: JWT tokens stored in secure, HTTP-only cookies for enhanced security
- **Spring Security Integration**: Custom JWT authentication filter with Spring Security
- **User Registration & Login**: Complete user registration and login flow
- **Password Encryption**: BCrypt password hashing for secure password storage
- **Role-based Access**: Support for different user roles (PRIVATE_SELLER, DEALER, etc.)

### 👤 User Management
- **User Profiles**: Complete user profile management with personal information
- **Account Status Tracking**: Active/inactive user status management
- **Profile Editing**: Users can update their profile information
- **Account Types**: Support for different seller types (Private Seller, Dealer)

### 🎨 Frontend & UI
- **Qute Templating**: Modern templating engine with Spring Boot integration
- **HTMX Integration**: Dynamic, interactive UI without complex JavaScript
- **Responsive Design**: Mobile-first responsive design with Tailwind CSS
- **Modern UI Components**: Clean, professional interface with proper navigation
- **Dynamic Content**: Real-time updates using HTMX for seamless user experience

### 🚲 Bike Listings & Marketplace
- **Comprehensive Listings**: Detailed bike specifications including frame material, groupset, condition
- **Privacy Protection**: Seller address privacy with neighborhood-level visibility until escrow activation
- **Advanced Search**: Filter by bike type, price range, condition, location, brand, and more
- **Photo Management**: Support for multiple high-quality photos with primary photo designation
- **Listing Management**: Create, edit, activate/deactivate, and mark listings as sold
- **Reservation System**: 24-hour reservation holds for buyers with automatic expiration
- **Condition Tiers**: Standardized condition ratings (New, Excellent, Good, Fair, Needs TLC)
- **Serial Number Tracking**: Unique serial number validation to prevent duplicate listings
- **Location-based Search**: Find bikes in specific cities or neighborhoods
- **Seller Verification**: Clear distinction between private sellers and bike shops

### 🏗️ Architecture & Development
- **Clean Architecture**: Well-structured codebase with clear separation of concerns
- **Domain-Driven Design**: Organized around business domains (User, Authentication, etc.)
- **RESTful APIs**: Clean REST endpoints for all user operations
- **Database Migrations**: Hibernate-managed database schema with automatic migrations
- **Comprehensive Testing**: Unit and integration tests with Testcontainers
- **Development Profile**: Optimized development setup with hot reloading

#### Docker Runtime Setup
The application supports multiple Docker runtimes. For most setups, Testcontainers will auto-detect your Docker environment. However, if you're using alternative runtimes, you may need to set environment variables:

**Quick Setup (Recommended):**
```bash
# Auto-detect and configure your Docker runtime
source scripts/setup-docker-env.sh
mvn test
```

**Alternative: Environment File**
```bash
# Copy and customize environment variables
cp .env.example .env
# Edit .env with your preferred settings, then:
source .env
mvn test
```

**Manual Setup for Different Runtimes:**

- **Docker Desktop**: Works out of the box, no configuration needed
- **Rancher Desktop (macOS)**:
  ```bash
  export DOCKER_HOST=unix://$HOME/.rd/docker.sock
  export TESTCONTAINERS_RYUK_DISABLED=true
  ```
- **Podman (macOS)**:
  ```bash
  export DOCKER_HOST=unix://$(podman machine inspect --format '{{.ConnectionInfo.PodmanSocket.Path}}')
  export TESTCONTAINERS_DOCKER_SOCKET_OVERRIDE=/var/run/docker.sock
  export TESTCONTAINERS_RYUK_DISABLED=true
  ```
- **Colima**:
  ```bash
  export DOCKER_HOST=unix://$HOME/.colima/default/docker.sock
  export TESTCONTAINERS_DOCKER_SOCKET_OVERRIDE=/var/run/docker.sock
  ```

## Development Notes

### Qute Template Engine with Java Records
When using Java records with Qute templates, use explicit conditional blocks instead of safe navigation operators:

```html
<!-- ✅ Correct: Use explicit conditionals -->
{#if user}{user.fullName()}{#else}Not provided{/if}

<!-- ❌ Avoid: Safe navigation with method calls -->
{user?.fullName() ?: 'Not provided'}
```

### JWT Authentication Implementation
- JWT tokens are stored in HTTP-only cookies for security
- Custom `JwtAuthenticationFilter` handles both Authorization headers and cookies
- Supports both Bearer tokens and cookie-based authentication
- Automatic token validation and Spring Security context setup

### Database Configuration
- Uses PostgreSQL with Hibernate for ORM
- Development profile configured for automatic schema updates
- Environment variables for database credentials:
  - `DB_USERNAME`: Database username (default: bikeana_dev)
  - `DB_PASSWORD`: Database password (default: dev_password)

### Development Environment
- **Docker Compose**: Provides PostgreSQL and pgAdmin containers
- **Development Script**: `./scripts/start-dev.sh` for one-command startup
- **Database Port**: PostgreSQL runs on port 5433 (to avoid conflicts with local installations)
- **pgAdmin**: Web-based database administration tool on port 8081
- **Hot Reloading**: Spring Boot DevTools enabled for automatic restarts

### Testing Strategy
- Testcontainers for integration testing with real PostgreSQL
- Supports multiple Docker runtimes (Docker Desktop, Rancher Desktop, Podman, Colima)
- Automatic Docker environment detection and configuration

## API Endpoints

### Authentication
- `POST /htmx/auth/signup` - User registration
- `POST /htmx/auth/login` - User login
- `POST /htmx/auth/logout` - User logout

### User Management
- `GET /profile` - View user profile
- `GET /htmx/users/profile/edit` - Get profile edit form
- `PUT /htmx/users/profile` - Update user profile

### Bike Listings (REST API)
- `GET /api/listings` - Get all active listings with pagination and filtering
- `GET /api/listings/{id}` - Get specific listing by ID
- `GET /api/listings/slug/{slug}` - Get specific listing by SEO slug
- `GET /api/listings/search` - Search listings with multiple criteria
- `GET /api/listings/my-listings` - Get current user's listings
- `POST /api/listings` - Create new listing (authenticated sellers only)
- `PATCH /api/listings/{id}/status` - Update listing status
- `POST /api/listings/{id}/reserve` - Reserve listing for purchase
- `DELETE /api/listings/{id}` - Delete listing (soft delete)

### Photo Management
- `POST /api/listings/{id}/photos` - Upload photos for listing
- `PATCH /api/listings/{id}/photos/{photoId}/primary` - Set primary photo
- `DELETE /api/listings/{id}/photos/{photoId}` - Delete photo

### Bike Listings (HTMX)
- `POST /htmx/listings/create` - Create listing via form submission
- `POST /htmx/listings/{id}/status` - Update listing status
- `POST /htmx/listings/{id}/reserve` - Reserve listing

### Pages
- `GET /` - Home page (shows authenticated/non-authenticated views)
- `GET /signup` - Registration page
- `GET /login` - Login page
- `GET /dashboard` - User dashboard (authenticated users only)
- `GET /profile` - User profile page (authenticated users only)
- `GET /listings` - Browse all bike listings with search and filters
- `GET /listings/{slug}` - Individual listing detail page
- `GET /listings/create` - Create new listing page (authenticated sellers only)
- `GET /my-listings` - User's own listings management page

All HTMX endpoints return HTML fragments for dynamic page updates.

## Next Milestone
✅ Define tech-stack and create project skeleton
✅ Implement user authentication and profile management
✅ Implement core marketplace features (bike listings)
🔄 Implement photo upload and management
🔄 Implement payment and escrow system

## Architecture

```mermaid
sequenceDiagram
    participant Seller
    participant Marketplace
    participant Buyer

    Seller->>Marketplace: createAuction(bike)
    Marketplace-->>Buyer: showAuction(id)
    Buyer->>Marketplace: placeBid(id, $)
    Marketplace-->>Marketplace: determineWinner
    Marketplace->>Buyer: chargeAndEscrow()
    Marketplace-->>Seller: sendPickupInfo()
    Seller->>Buyer: deliverBike
    Seller->>Marketplace: markDelivered()
    Marketplace->>Seller: releaseEscrow(payMinusFee)
    Buyer->>Marketplace: review()
    Seller->>Marketplace: review()
```

